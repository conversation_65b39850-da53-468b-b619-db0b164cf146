#!/usr/bin/env python3
"""
Simple test for PDF processing only.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_pdf_processing():
    """Test PDF processing with the specific test file."""
    
    from services.api.clients.pdf_processor import PDFProcessor
    
    # Test data
    test_s3_url = "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf"
    
    print(f"🧪 Testing PDF processing")
    print(f"📎 S3 URL: {test_s3_url}")
    
    try:
        async with PDFProcessor() as processor:
            extracted_text = await processor.process_resume_pdf(test_s3_url)
        
        print(f"✅ PDF processing successful!")
        print(f"📄 Extracted {len(extracted_text)} characters")
        print(f"📝 First 500 characters:")
        print("-" * 50)
        print(extracted_text[:500])
        print("-" * 50)
        
        return True, extracted_text
        
    except Exception as e:
        print(f"❌ PDF processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

async def test_resume_parsing(text):
    """Test resume parsing with extracted text."""
    
    from services.api.clients.resume_parser import parse_resume_text
    
    print(f"\n🧪 Testing resume parsing")
    
    try:
        profile_data = parse_resume_text(text)
        
        print("✅ Resume parsing successful!")
        print(f"👤 Name: {profile_data.full_name}")
        print(f"📧 Email: {profile_data.email}")
        print(f"📞 Phone: {profile_data.phone}")
        print(f"📍 Location: {profile_data.location}")
        print(f"📝 Summary: {profile_data.summary[:100] if profile_data.summary else 'None'}...")
        print(f"🎯 Skills: {profile_data.skills}")
        print(f"💼 Experience: {len(profile_data.work_experience)} positions")
        print(f"🎓 Education: {len(profile_data.education)} entries")
        
        return True, profile_data
        
    except Exception as e:
        print(f"❌ Resume parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

async def main():
    """Run the simple tests."""
    print("🚀 Simple Resume Processing Test")
    print("=" * 50)
    
    # Step 1: Test PDF processing
    success1, extracted_text = await test_pdf_processing()
    
    # Step 2: Test resume parsing (if PDF processing succeeded)
    success2, profile_data = False, None
    if success1 and extracted_text:
        success2, profile_data = await test_resume_parsing(extracted_text)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    results = [
        ("PDF Processing", success1),
        ("Resume Parsing", success2)
    ]
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 Basic resume processing components are working!")
    else:
        print("❌ Some components need attention")

if __name__ == "__main__":
    asyncio.run(main())
