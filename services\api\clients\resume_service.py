"""
Resume processing service for complete resume ingestion pipeline.

This module provides the main service for processing resumes from S3 URLs,
extracting structured data, generating vectors, and storing in Qdrant.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import uuid4, uuid5, NAMESPACE_DNS, UUID
import hashlib

from common.models import ResumeProfileData, ResumeProfileVector
from common.logging import get_logger
from common.utils import normalize_text
from services.api.clients.pdf_processor import PDFProcessor, PDFProcessingError
from services.api.clients.resume_parser import ResumeParser
from services.api.clients.vector_service import VectorGenerationService
from services.api.clients.qdrant import get_qdrant_resumes_client
from services.api.clients.database import get_database_client

logger = get_logger(__name__)


class ResumeProcessingError(Exception):
    """Custom exception for resume processing errors."""
    
    def __init__(self, message: str, error_type: str = "processing_error", details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.error_type = error_type
        self.details = details or {}


class ResumeProcessingResult:
    """Result of resume processing operation."""
    
    def __init__(
        self,
        success: bool,
        email: str,
        profile_data: Optional[ResumeProfileData] = None,
        error_message: Optional[str] = None,
        error_type: Optional[str] = None,
        processing_time: Optional[float] = None
    ):
        self.success = success
        self.email = email
        self.profile_data = profile_data
        self.error_message = error_message
        self.error_type = error_type
        self.processing_time = processing_time


class ResumeProcessingService:
    """
    Service for processing resumes from S3 URLs to dual storage (Qdrant + Supabase).

    This service orchestrates the complete pipeline:
    1. Download and extract text from PDF
    2. Parse text into structured data
    3. Generate multi-vector embeddings
    4. Store vectors in Qdrant resumes collection
    5. Store metadata in Supabase resume_metadata table
    """

    def __init__(self):
        """Initialize the resume processing service."""
        self._pdf_processor = None
        self._resume_parser = None
        self._vector_service = None
        self._qdrant_client = None
        self._database_client = None
        self._initialized = False

    @staticmethod
    def _generate_point_id_from_email(email: str) -> str:
        """
        Generate a deterministic UUID from an email address.

        Args:
            email: Email address

        Returns:
            UUID string that can be used as Qdrant point ID
        """
        # Use UUID5 with DNS namespace to generate deterministic UUID from email
        return str(uuid5(NAMESPACE_DNS, email.lower().strip()))
    
    async def initialize(self):
        """Initialize all service components."""
        if self._initialized:
            return
        
        try:
            logger.info("Initializing resume processing service")
            
            # Initialize vector service
            self._vector_service = VectorGenerationService()
            await self._vector_service.initialize()
            
            # Initialize resume parser
            self._resume_parser = ResumeParser()
            
            # Initialize Qdrant client
            self._qdrant_client = get_qdrant_resumes_client()

            # Ensure resumes collection exists
            collection_exists = await self._qdrant_client.ensure_collection_exists()
            if not collection_exists:
                raise ResumeProcessingError(
                    "Failed to ensure resumes collection exists",
                    error_type="initialization_error"
                )

            # Initialize database client
            self._database_client = await get_database_client()

            self._initialized = True
            logger.info("Resume processing service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize resume processing service: {e}")
            raise ResumeProcessingError(
                f"Service initialization failed: {e}",
                error_type="initialization_error",
                details={"original_error": str(e)}
            )
    
    async def _generate_resume_vectors(self, profile_data: ResumeProfileData) -> Dict[str, List[float]]:
        """
        Generate multi-vector embeddings for resume profile data.
        
        Args:
            profile_data: Structured resume data
            
        Returns:
            Dictionary of named vectors
        """
        if not self._vector_service:
            raise ResumeProcessingError(
                "Vector service not initialized",
                error_type="service_error"
            )
        
        try:
            # Create text representations for different sections
            sections = {}
            
            # Skills vector
            if profile_data.skills:
                sections['skills'] = " | ".join(profile_data.skills)
            
            # Experience vector
            if profile_data.work_experience:
                exp_texts = []
                for exp in profile_data.work_experience:
                    exp_text = f"{exp.job_title} at {exp.company_name}"
                    if exp.description:
                        exp_text += f" - {exp.description}"
                    exp_texts.append(exp_text)
                sections['experience'] = " | ".join(exp_texts)
            
            # Education vector
            if profile_data.education:
                edu_texts = []
                for edu in profile_data.education:
                    edu_text = f"{edu.degree} in {edu.field_of_study} from {edu.institution_name}"
                    edu_texts.append(edu_text)
                sections['education'] = " | ".join(edu_texts)
            
            # Summary vector
            if profile_data.summary:
                sections['summary'] = profile_data.summary
            
            # Certifications vector
            if profile_data.certifications:
                cert_texts = [f"{cert.name} from {cert.issuing_organization}" for cert in profile_data.certifications]
                sections['certifications'] = " | ".join(cert_texts)
            
            # Volunteer experience vector
            if profile_data.volunteer_experience:
                vol_texts = []
                for vol in profile_data.volunteer_experience:
                    vol_text = f"{vol.role} at {vol.organization}"
                    if vol.description:
                        vol_text += f" - {vol.description}"
                    vol_texts.append(vol_text)
                sections['volunteer_experience'] = " | ".join(vol_texts)
            
            # Languages vector
            if profile_data.languages:
                sections['languages'] = " | ".join(profile_data.languages)
            
            # Combined vector (all sections together)
            all_text_parts = []
            if profile_data.full_name:
                all_text_parts.append(f"Name: {profile_data.full_name}")
            if profile_data.summary:
                all_text_parts.append(f"Summary: {profile_data.summary}")
            if sections.get('skills'):
                all_text_parts.append(f"Skills: {sections['skills']}")
            if sections.get('experience'):
                all_text_parts.append(f"Experience: {sections['experience']}")
            if sections.get('education'):
                all_text_parts.append(f"Education: {sections['education']}")
            
            sections['combined'] = " | ".join(all_text_parts)
            
            # Generate vectors for each section
            vectors = {}
            vector_field_mapping = {
                'skills': 'skills_vector',
                'experience': 'experience_vector',
                'education': 'experience_vector',  # Use same field for education
                'summary': 'summary_vector',
                'certifications': 'certifications_vector',
                'volunteer_experience': 'volunteer_experience_vector',
                'languages': 'languages_vector',
                'combined': 'combined_vector'
            }
            
            for section_name, text in sections.items():
                if text and text.strip():
                    vector_field = vector_field_mapping.get(section_name)
                    if vector_field:
                        vector = await self._vector_service.generate_embedding_from_text(text)
                        vectors[vector_field] = vector
            
            logger.debug(f"Generated {len(vectors)} vectors for resume")
            return vectors
            
        except Exception as e:
            logger.error(f"Failed to generate resume vectors: {e}")
            raise ResumeProcessingError(
                f"Vector generation failed: {e}",
                error_type="vector_error",
                details={"original_error": str(e)}
            )
    
    async def process_resume(
        self,
        email: str,
        resume_s3_url: str,
        filename: Optional[str] = None
    ) -> ResumeProcessingResult:
        """
        Process a resume from S3 URL to Qdrant storage.
        
        Args:
            email: Email address of the volunteer
            resume_s3_url: S3 URL pointing to the PDF resume
            filename: Optional original filename
            
        Returns:
            ResumeProcessingResult with processing details
        """
        start_time = datetime.utcnow()
        
        try:
            logger.info(
                f"Starting resume processing for {email}",
                resume_s3_url=resume_s3_url,
                filename=filename
            )
            
            # Ensure service is initialized
            if not self._initialized:
                await self.initialize()

            # Step 0: Store initial resume metadata in Supabase (optional)
            logger.info("Step 0: Storing initial resume metadata in Supabase")

            # Generate deterministic point ID from email for Qdrant
            point_id = self._generate_point_id_from_email(email)

            # Try to store initial metadata (gracefully handle table not existing)
            metadata_record_id = None
            try:
                metadata_record_id = await self._database_client.store_resume_metadata(
                    email=email,
                    resume_s3_url=resume_s3_url,
                    filename=filename,
                    qdrant_point_id=UUID(point_id),
                    processing_status="processing",
                    extraction_status="pending"
                )

                if metadata_record_id:
                    logger.info(f"Successfully stored initial resume metadata for {email}")
                else:
                    logger.warning(f"Failed to store initial resume metadata for {email}, continuing with processing")
            except Exception as metadata_error:
                logger.warning(f"Could not store resume metadata (table may not exist): {metadata_error}")
                logger.info("Continuing with resume processing without Supabase metadata")

            # Step 1: Download and extract text from PDF
            logger.info("Step 1: Downloading and extracting text from PDF")
            async with PDFProcessor() as pdf_processor:
                extracted_text = await pdf_processor.process_resume_pdf(resume_s3_url)
            
            if not extracted_text or len(extracted_text.strip()) < 100:
                # Update metadata with extraction failure
                if metadata_record_id:
                    await self._database_client.update_resume_metadata(
                        record_id=metadata_record_id,
                        processing_status="failed",
                        extraction_status="failed",
                        error_message="Insufficient text extracted from PDF",
                        error_type="extraction_error"
                    )

                raise ResumeProcessingError(
                    "Insufficient text extracted from PDF",
                    error_type="extraction_error",
                    details={"text_length": len(extracted_text) if extracted_text else 0}
                )
            
            logger.info(f"Successfully extracted {len(extracted_text)} characters from PDF")

            # Update metadata with successful extraction (optional)
            if metadata_record_id:
                try:
                    await self._database_client.update_resume_metadata(
                        record_id=metadata_record_id,
                        extraction_status="success"
                    )
                except Exception as e:
                    logger.warning(f"Could not update resume metadata: {e}")

            # Step 2: Parse text into structured data
            logger.info("Step 2: Parsing text into structured data")
            profile_data = self._resume_parser.parse_resume_text(extracted_text)
            
            # Ensure email is set in profile data
            if not profile_data.email:
                profile_data.email = email
            
            logger.info(
                f"Successfully parsed resume data",
                skills_count=len(profile_data.skills),
                experience_count=len(profile_data.work_experience),
                education_count=len(profile_data.education)
            )
            
            # Step 3: Generate multi-vector embeddings
            logger.info("Step 3: Generating multi-vector embeddings")
            vectors = await self._generate_resume_vectors(profile_data)
            
            if not vectors:
                raise ResumeProcessingError(
                    "No vectors generated from resume data",
                    error_type="vector_error"
                )
            
            logger.info(f"Successfully generated {len(vectors)} vectors")
            
            # Step 4: Store in Qdrant
            logger.info("Step 4: Storing in Qdrant resumes collection")

            # Generate deterministic point ID from email
            point_id = self._generate_point_id_from_email(email)

            # Prepare metadata
            metadata = {
                'email': email,
                'full_name': profile_data.full_name,
                'skills': profile_data.skills,
                'languages': profile_data.languages,
                'location': profile_data.location,
                'resume_s3_url': resume_s3_url,
                'filename': filename,
                'extraction_timestamp': datetime.utcnow().isoformat(),
                'profile_data': profile_data.dict()
            }

            # Store in Qdrant
            success = await self._qdrant_client.upsert_resume_point(
                point_id=point_id,  # Use UUID as point ID
                vectors=vectors,
                metadata=metadata
            )
            
            if not success:
                raise ResumeProcessingError(
                    "Failed to store resume data in Qdrant",
                    error_type="storage_error"
                )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            processing_time_ms = int(processing_time * 1000)

            # Step 5: Update Supabase metadata with completion (optional)
            logger.info("Step 5: Updating Supabase metadata with completion")
            if metadata_record_id:
                try:
                    await self._database_client.update_resume_metadata(
                        record_id=metadata_record_id,
                        profile_data=profile_data.dict(),
                        processing_status="completed",
                        processing_time_ms=processing_time_ms
                    )
                except Exception as e:
                    logger.warning(f"Could not update resume metadata on completion: {e}")

            logger.info(
                f"Resume processing completed successfully for {email}",
                processing_time=processing_time,
                vectors_stored=len(vectors),
                supabase_updated=bool(metadata_record_id)
            )

            return ResumeProcessingResult(
                success=True,
                email=email,
                profile_data=profile_data,
                processing_time=processing_time
            )
            
        except Exception as e:
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            processing_time_ms = int(processing_time * 1000)

            if isinstance(e, (PDFProcessingError, ResumeProcessingError)):
                error_type = e.error_type
                error_message = str(e)
            else:
                error_type = "unexpected_error"
                error_message = f"Unexpected error during resume processing: {e}"

            # Update Supabase metadata with failure
            if 'metadata_record_id' in locals() and metadata_record_id:
                try:
                    await self._database_client.update_resume_metadata(
                        record_id=metadata_record_id,
                        processing_status="failed",
                        processing_time_ms=processing_time_ms,
                        error_message=error_message,
                        error_type=error_type
                    )
                except Exception as update_error:
                    logger.error(f"Failed to update resume metadata on error: {update_error}")

            logger.error(
                f"Resume processing failed for {email}: {error_message}",
                error_type=error_type,
                processing_time=processing_time
            )

            return ResumeProcessingResult(
                success=False,
                email=email,
                error_message=error_message,
                error_type=error_type,
                processing_time=processing_time
            )


# Global service instance
_resume_service = None


async def get_resume_processing_service() -> ResumeProcessingService:
    """
    Get the global resume processing service instance.
    
    Returns:
        ResumeProcessingService instance
    """
    global _resume_service
    
    if _resume_service is None:
        _resume_service = ResumeProcessingService()
        await _resume_service.initialize()
    
    return _resume_service


async def process_resume(
    email: str,
    resume_s3_url: str,
    filename: Optional[str] = None
) -> ResumeProcessingResult:
    """
    Convenience function to process a resume.
    
    Args:
        email: Email address of the volunteer
        resume_s3_url: S3 URL pointing to the PDF resume
        filename: Optional original filename
        
    Returns:
        ResumeProcessingResult with processing details
    """
    service = await get_resume_processing_service()
    return await service.process_resume(email, resume_s3_url, filename)
