#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create the resume_metadata table in Supabase.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def create_resume_metadata_table():
    """Create the resume_metadata table in Supabase."""
    
    from services.api.clients.database import get_database_client
    
    print("🗄️  Creating resume_metadata table in Supabase...")
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Read the SQL file
        sql_file = Path(__file__).parent / "sql" / "create_resume_metadata_table.sql"
        
        if not sql_file.exists():
            print(f"❌ SQL file not found: {sql_file}")
            return False
        
        with open(sql_file, 'r') as f:
            sql_content = f.read()
        
        print(f"📄 Read SQL file: {sql_file}")
        print(f"📝 SQL content length: {len(sql_content)} characters")
        
        # Execute the SQL using Supabase client
        # Note: Supabase Python client doesn't directly support DDL operations
        # This would typically be done through the Supabase dashboard or API
        print("⚠️  Note: Table creation should be done through Supabase dashboard")
        print("📋 Please execute the following SQL in your Supabase SQL editor:")
        print("=" * 60)
        print(sql_content)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating resume metadata table: {e}")
        return False

async def test_table_access():
    """Test if we can access the resume_metadata table."""
    
    from services.api.clients.database import get_database_client
    
    print("\n🧪 Testing resume_metadata table access...")
    
    try:
        db_client = await get_database_client()
        
        # Try to query the table (this will fail if table doesn't exist)
        response = db_client.client.table("resume_metadata").select("*").limit(1).execute()
        
        print("✅ resume_metadata table exists and is accessible")
        print(f"📊 Current record count: {len(response.data)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cannot access resume_metadata table: {e}")
        print("💡 Please create the table using the SQL provided above")
        return False

async def main():
    """Main function."""
    print("🚀 Resume Metadata Table Setup")
    print("=" * 40)
    
    # Step 1: Show SQL for table creation
    success1 = await create_resume_metadata_table()
    
    # Step 2: Test table access
    success2 = await test_table_access()
    
    print("\n" + "=" * 40)
    if success1 and success2:
        print("✅ Resume metadata table is ready!")
    elif success1:
        print("⚠️  Please create the table using the SQL provided above")
    else:
        print("❌ Setup failed")

if __name__ == "__main__":
    asyncio.run(main())
