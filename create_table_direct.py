#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create the resume_metadata table using Supabase RPC.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def create_table_via_rpc():
    """Create the resume_metadata table using Supabase RPC."""
    
    from services.api.clients.database import get_database_client
    
    print("🗄️  Creating resume_metadata table via Supabase RPC...")
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # SQL to create the table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS public.resume_metadata (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            email VARCHAR(255) NOT NULL,
            volunteer_id UUID,
            resume_s3_url TEXT NOT NULL,
            filename VARCHAR(255),
            processing_status VARCHAR(50) NOT NULL DEFAULT 'processing',
            extraction_status VARCHAR(50) NOT NULL DEFAULT 'pending',
            qdrant_point_id UUID,
            skills_extracted TEXT[] DEFAULT '{}',
            languages_extracted TEXT[] DEFAULT '{}',
            full_name_extracted VARCHAR(255),
            location_extracted VARCHAR(255),
            phone_extracted VARCHAR(255),
            summary_extracted TEXT,
            work_experience_count INTEGER DEFAULT 0,
            education_count INTEGER DEFAULT 0,
            certifications_count INTEGER DEFAULT 0,
            volunteer_experience_count INTEGER DEFAULT 0,
            projects_count INTEGER DEFAULT 0,
            processing_time_ms INTEGER,
            error_message TEXT,
            error_type VARCHAR(100),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            processed_at TIMESTAMP WITH TIME ZONE,
            
            CONSTRAINT fk_resume_volunteer 
                FOREIGN KEY (volunteer_id) 
                REFERENCES public.volunteers(id) 
                ON DELETE SET NULL,
            
            CONSTRAINT unique_email_resume_url 
                UNIQUE (email, resume_s3_url)
        );
        """
        
        # Try to execute via RPC
        try:
            response = db_client.client.rpc('exec_sql', {'sql': create_table_sql}).execute()
            print("✅ Table created successfully via RPC")
            return True
        except Exception as rpc_error:
            print(f"⚠️  RPC method not available: {rpc_error}")
            
            # Alternative: Try to create a dummy record to test if table exists
            try:
                # This will fail if table doesn't exist, which is what we want to test
                db_client.client.table("resume_metadata").select("id").limit(1).execute()
                print("✅ Table already exists")
                return True
            except Exception as table_error:
                print(f"❌ Table does not exist and cannot be created via Python client")
                print("💡 Please create the table manually in Supabase SQL editor")
                return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def create_indexes():
    """Create indexes for the resume_metadata table."""
    
    from services.api.clients.database import get_database_client
    
    print("\n🔍 Creating indexes for resume_metadata table...")
    
    indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_email ON public.resume_metadata(email);",
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_volunteer_id ON public.resume_metadata(volunteer_id);",
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_processing_status ON public.resume_metadata(processing_status);",
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_extraction_status ON public.resume_metadata(extraction_status);",
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_created_at ON public.resume_metadata(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_qdrant_point_id ON public.resume_metadata(qdrant_point_id);",
        "CREATE INDEX IF NOT EXISTS idx_resume_metadata_skills ON public.resume_metadata USING GIN(skills_extracted);"
    ]
    
    try:
        db_client = await get_database_client()
        
        for sql in indexes_sql:
            try:
                response = db_client.client.rpc('exec_sql', {'sql': sql}).execute()
                print(f"✅ Created index: {sql.split('idx_')[1].split(' ')[0]}")
            except Exception as e:
                print(f"⚠️  Could not create index via RPC: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Error creating indexes: {e}")
        return False

async def test_table_operations():
    """Test basic operations on the resume_metadata table."""
    
    from services.api.clients.database import get_database_client
    
    print("\n🧪 Testing resume_metadata table operations...")
    
    try:
        db_client = await get_database_client()
        
        # Test 1: Check if table exists
        response = db_client.client.table("resume_metadata").select("*").limit(1).execute()
        print("✅ Table exists and is queryable")
        
        # Test 2: Test insert operation
        test_data = {
            "email": "<EMAIL>",
            "resume_s3_url": "https://test-bucket.s3.amazonaws.com/test.pdf",
            "filename": "test.pdf",
            "processing_status": "processing"
        }
        
        insert_response = db_client.client.table("resume_metadata").insert(test_data).execute()
        if insert_response.data:
            record_id = insert_response.data[0]["id"]
            print(f"✅ Insert operation successful: {record_id}")
            
            # Test 3: Test update operation
            update_response = db_client.client.table("resume_metadata").update({
                "processing_status": "completed"
            }).eq("id", record_id).execute()
            
            if update_response.data:
                print("✅ Update operation successful")
            
            # Test 4: Test delete operation (cleanup)
            delete_response = db_client.client.table("resume_metadata").delete().eq("id", record_id).execute()
            if delete_response.data:
                print("✅ Delete operation successful (test cleanup)")
        
        return True
        
    except Exception as e:
        print(f"❌ Table operations test failed: {e}")
        return False

async def main():
    """Main function."""
    print("🚀 Resume Metadata Table Creation")
    print("=" * 50)
    
    # Step 1: Try to create table
    success1 = await create_table_via_rpc()
    
    # Step 2: Create indexes (if table creation succeeded)
    success2 = True
    if success1:
        success2 = await create_indexes()
    
    # Step 3: Test table operations
    success3 = await test_table_operations()
    
    print("\n" + "=" * 50)
    if success1 and success2 and success3:
        print("✅ Resume metadata table is fully ready!")
    elif success3:
        print("✅ Resume metadata table exists and is working!")
    else:
        print("❌ Please create the table manually in Supabase")
        print("\n📋 SQL to execute in Supabase SQL editor:")
        print("-" * 40)
        
        # Read and display the SQL file
        sql_file = Path(__file__).parent / "sql" / "create_resume_metadata_table.sql"
        if sql_file.exists():
            with open(sql_file, 'r') as f:
                print(f.read())

if __name__ == "__main__":
    asyncio.run(main())
