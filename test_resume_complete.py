#!/usr/bin/env python3
"""
Complete integration test for resume processing with the specific test file.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_resume_processing_complete():
    """Test the complete resume processing pipeline with the specific test file."""
    
    from services.api.clients.resume_service import process_resume
    
    # Test data - using the specific file mentioned in requirements
    test_email = "<EMAIL>"
    test_s3_url = "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf"
    test_filename = "AbhishekBisht[5y_0m].pdf"
    
    print(f"🧪 Testing complete resume processing pipeline")
    print(f"📧 Email: {test_email}")
    print(f"📎 S3 URL: {test_s3_url}")
    print(f"📄 Filename: {test_filename}")
    print("=" * 70)
    
    try:
        # Process the resume
        print("🚀 Starting resume processing...")
        result = await process_resume(
            email=test_email,
            resume_s3_url=test_s3_url,
            filename=test_filename
        )
        
        if result.success:
            print("✅ Resume processing completed successfully!")
            print(f"⏱️  Processing time: {result.processing_time:.2f} seconds")
            
            if result.profile_data:
                print("\n📊 Extracted Profile Data:")
                print(f"👤 Name: {result.profile_data.full_name}")
                print(f"📧 Email: {result.profile_data.email}")
                print(f"📞 Phone: {result.profile_data.phone}")
                print(f"📍 Location: {result.profile_data.location}")
                print(f"📝 Summary: {result.profile_data.summary[:100] if result.profile_data.summary else 'None'}...")
                print(f"🎯 Skills: {len(result.profile_data.skills)} skills found")
                print(f"💼 Experience: {len(result.profile_data.work_experience)} positions")
                print(f"🎓 Education: {len(result.profile_data.education)} entries")
                print(f"🏆 Certifications: {len(result.profile_data.certifications)} certifications")
                print(f"🤝 Volunteer: {len(result.profile_data.volunteer_experience)} experiences")
                print(f"🗣️  Languages: {len(result.profile_data.languages)} languages")
                
                if result.profile_data.skills:
                    print(f"🔧 Skills sample: {', '.join(result.profile_data.skills[:10])}")
                
                if result.profile_data.work_experience:
                    exp = result.profile_data.work_experience[0]
                    print(f"💼 Latest job: {exp.job_title} at {exp.company_name}")
                
                if result.profile_data.education:
                    edu = result.profile_data.education[0]
                    print(f"🎓 Education: {edu.degree} from {edu.institution_name}")
        else:
            print("❌ Resume processing failed")
            print(f"🚫 Error type: {result.error_type}")
            print(f"💬 Error message: {result.error_message}")
            
        return result.success
        
    except Exception as e:
        print(f"❌ Unexpected error during resume processing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def verify_qdrant_storage():
    """Verify that the resume data was stored in Qdrant."""
    
    from services.api.clients.qdrant import get_qdrant_resumes_client
    from services.api.clients.resume_service import ResumeProcessingService
    
    print("\n🔍 Verifying Qdrant storage...")
    
    try:
        client = get_qdrant_resumes_client()
        
        # Generate the same point ID that would be used for the test email
        test_email = "<EMAIL>"
        point_id = ResumeProcessingService._generate_point_id_from_email(test_email)
        
        print(f"🔑 Looking for point ID: {point_id}")
        
        # Try to retrieve the point
        point = await client.get_point(point_id)
        
        if point:
            print("✅ Resume data found in Qdrant!")
            payload = point.get('payload', {})
            print(f"📧 Stored email: {payload.get('email')}")
            print(f"👤 Stored name: {payload.get('full_name')}")
            print(f"🎯 Stored skills: {len(payload.get('skills', []))} skills")
            print(f"📄 Filename: {payload.get('filename')}")
            print(f"⏰ Extraction time: {payload.get('extraction_timestamp')}")
            
            # Check vectors
            vectors = point.get('vector', {})
            if isinstance(vectors, dict):
                print(f"🔢 Vector fields: {list(vectors.keys())}")
                for field_name, vector in vectors.items():
                    print(f"   {field_name}: {len(vector)} dimensions")
            
            return True
        else:
            print("❌ Resume data not found in Qdrant")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying Qdrant storage: {e}")
        return False

async def verify_supabase_storage():
    """Verify that the resume metadata was stored in Supabase."""
    
    from services.api.clients.database import get_database_client
    
    print("\n🗄️  Verifying Supabase storage...")
    
    try:
        db_client = await get_database_client()
        test_email = "<EMAIL>"
        
        # Try to get resume metadata
        metadata_records = await db_client.get_resume_metadata_by_email(test_email)
        
        if metadata_records:
            print(f"✅ Found {len(metadata_records)} resume metadata record(s) in Supabase!")
            
            for i, record in enumerate(metadata_records, 1):
                print(f"\n📋 Record {i}:")
                print(f"   📧 Email: {record.get('email')}")
                print(f"   📄 Filename: {record.get('filename')}")
                print(f"   📊 Processing status: {record.get('processing_status')}")
                print(f"   🔍 Extraction status: {record.get('extraction_status')}")
                print(f"   👤 Name extracted: {record.get('full_name_extracted')}")
                print(f"   🎯 Skills count: {len(record.get('skills_extracted', []))}")
                print(f"   💼 Experience count: {record.get('work_experience_count', 0)}")
                print(f"   🎓 Education count: {record.get('education_count', 0)}")
                print(f"   ⏱️  Processing time: {record.get('processing_time_ms')} ms")
                print(f"   🔑 Qdrant point ID: {record.get('qdrant_point_id')}")
                
                if record.get('error_message'):
                    print(f"   ❌ Error: {record.get('error_message')}")
            
            return True
        else:
            print("❌ No resume metadata found in Supabase")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying Supabase storage: {e}")
        print("💡 This might be because the resume_metadata table doesn't exist yet")
        return False

async def check_volunteer_linking():
    """Check if the resume is properly linked to a volunteer record."""
    
    from services.api.clients.database import get_database_client
    
    print("\n🔗 Checking volunteer record linking...")
    
    try:
        db_client = await get_database_client()
        test_email = "<EMAIL>"
        
        # Check if volunteer record exists
        volunteer_response = db_client.client.table("volunteers").select("*").eq("email", test_email).execute()
        
        if volunteer_response.data:
            volunteer = volunteer_response.data[0]
            print(f"✅ Volunteer record found!")
            print(f"   👤 Name: {volunteer.get('full_name')}")
            print(f"   📧 Email: {volunteer.get('email')}")
            print(f"   🎯 Skills: {len(volunteer.get('skills', []))} skills")
            print(f"   📍 Location: {volunteer.get('location')}")
            print(f"   🏢 Company: {volunteer.get('current_company')}")
            print(f"   💼 Position: {volunteer.get('current_position')}")
            
            # Check source types
            source_types = volunteer.get('source_types', [])
            print(f"   📊 Source types: {source_types}")
            
            if 'resume' in source_types:
                print("   ✅ Resume source type is recorded")
            else:
                print("   ⚠️  Resume source type not recorded")
            
            return True
        else:
            print("❌ No volunteer record found for this email")
            print("💡 This is expected if the volunteer hasn't been created yet")
            return False
            
    except Exception as e:
        print(f"❌ Error checking volunteer linking: {e}")
        return False

async def main():
    """Run the complete integration test."""
    print("🚀 Complete Resume Processing Integration Test")
    print("=" * 70)
    
    # Step 1: Process the resume
    success1 = await test_resume_processing_complete()
    
    # Step 2: Verify Qdrant storage
    success2 = await verify_qdrant_storage()
    
    # Step 3: Verify Supabase storage
    success3 = await verify_supabase_storage()
    
    # Step 4: Check volunteer linking
    success4 = await check_volunteer_linking()
    
    print("\n" + "=" * 70)
    print("📊 Integration Test Results:")
    
    results = [
        ("Resume Processing", success1),
        ("Qdrant Storage", success2),
        ("Supabase Storage", success3),
        ("Volunteer Linking", success4)
    ]
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed >= 2:  # At least processing and Qdrant should work
        print("🎉 Resume processing pipeline is working!")
        if passed < len(results):
            print("⚠️  Some components need setup (likely Supabase table)")
    else:
        print("❌ Resume processing pipeline needs attention")

if __name__ == "__main__":
    asyncio.run(main())
