#!/usr/bin/env python3
"""
Test script for vector-only resume processing functionality.

This script tests the simplified vector-only approach using actual PDF files
from the s3://skill-assessment-test/pdfs/ bucket.
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger
from services.api.clients.resume_service import (
    ResumeVectorOnlyService,
    process_resume_vector_only,
    get_vector_only_resume_service
)

logger = get_logger(__name__)


class VectorOnlyResumeTestRunner:
    """Test runner for vector-only resume processing."""
    
    def __init__(self):
        self.test_bucket = "skill-assessment-test"
        self.test_pdfs = [
            "sample_resume_1.pdf",
            "sample_resume_2.pdf", 
            "test_resume.pdf",
            "john_doe_resume.pdf"
        ]
        self.test_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
    
    def get_s3_url(self, filename: str) -> str:
        """Generate S3 URL for test file."""
        return f"https://{self.test_bucket}.s3.amazonaws.com/pdfs/{filename}"
    
    async def test_service_initialization(self):
        """Test that the vector-only service initializes correctly."""
        logger.info("Testing vector-only service initialization...")
        
        try:
            service = ResumeVectorOnlyService()
            await service.initialize()
            
            logger.info("✅ Vector-only service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Service initialization failed: {e}")
            return False
    
    async def test_single_resume_processing(self, email: str, s3_url: str, filename: str):
        """Test processing a single resume."""
        logger.info(f"Testing resume processing for {email} with file {filename}")
        
        try:
            start_time = time.time()
            
            # Process the resume using vector-only approach
            result = await process_resume_vector_only(
                email=email,
                resume_s3_url=s3_url,
                filename=filename
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            if result.success:
                logger.info(
                    f"✅ Resume processed successfully for {email}",
                    point_id=result.point_id,
                    text_length=result.text_length,
                    processing_time=result.processing_time,
                    total_time=total_time
                )
                return True
            else:
                logger.error(
                    f"❌ Resume processing failed for {email}",
                    error_type=result.error_type,
                    error_message=result.error_message
                )
                return False
                
        except Exception as e:
            logger.error(f"❌ Unexpected error processing resume for {email}: {e}")
            return False
    
    async def test_multiple_resumes(self):
        """Test processing multiple resumes."""
        logger.info("Testing multiple resume processing...")
        
        success_count = 0
        total_count = min(len(self.test_pdfs), len(self.test_emails))
        
        for i in range(total_count):
            email = self.test_emails[i]
            filename = self.test_pdfs[i]
            s3_url = self.get_s3_url(filename)
            
            success = await self.test_single_resume_processing(email, s3_url, filename)
            if success:
                success_count += 1
            
            # Add small delay between requests
            await asyncio.sleep(1)
        
        logger.info(f"Multiple resume test completed: {success_count}/{total_count} successful")
        return success_count, total_count
    
    async def test_error_handling(self):
        """Test error handling with invalid inputs."""
        logger.info("Testing error handling...")
        
        test_cases = [
            {
                "name": "Invalid S3 URL",
                "email": "<EMAIL>",
                "s3_url": "https://invalid-bucket.s3.amazonaws.com/nonexistent.pdf",
                "filename": "nonexistent.pdf"
            },
            {
                "name": "Malformed URL",
                "email": "<EMAIL>", 
                "s3_url": "not-a-valid-url",
                "filename": "test.pdf"
            }
        ]
        
        error_tests_passed = 0
        
        for test_case in test_cases:
            logger.info(f"Testing error case: {test_case['name']}")
            
            try:
                result = await process_resume_vector_only(
                    email=test_case["email"],
                    resume_s3_url=test_case["s3_url"],
                    filename=test_case["filename"]
                )
                
                if not result.success:
                    logger.info(f"✅ Error case handled correctly: {result.error_type}")
                    error_tests_passed += 1
                else:
                    logger.warning(f"⚠️ Expected error but processing succeeded for {test_case['name']}")
                    
            except Exception as e:
                logger.info(f"✅ Error case handled with exception: {e}")
                error_tests_passed += 1
        
        logger.info(f"Error handling test completed: {error_tests_passed}/{len(test_cases)} cases handled correctly")
        return error_tests_passed, len(test_cases)
    
    async def test_point_id_consistency(self):
        """Test that the same email generates the same point ID."""
        logger.info("Testing point ID consistency...")
        
        try:
            service = await get_vector_only_resume_service()
            
            email = "<EMAIL>"
            point_id_1 = service._generate_point_id_from_email(email)
            point_id_2 = service._generate_point_id_from_email(email)
            
            if point_id_1 == point_id_2:
                logger.info(f"✅ Point ID consistency verified: {point_id_1}")
                return True
            else:
                logger.error(f"❌ Point ID inconsistency: {point_id_1} != {point_id_2}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Point ID consistency test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all tests and provide a summary."""
        logger.info("Starting comprehensive vector-only resume processing tests...")
        
        test_results = {}
        
        # Test 1: Service initialization
        test_results["initialization"] = await self.test_service_initialization()
        
        # Test 2: Point ID consistency
        test_results["point_id_consistency"] = await self.test_point_id_consistency()
        
        # Test 3: Single resume processing (using first test file)
        if self.test_pdfs and self.test_emails:
            test_results["single_resume"] = await self.test_single_resume_processing(
                self.test_emails[0],
                self.get_s3_url(self.test_pdfs[0]),
                self.test_pdfs[0]
            )
        else:
            test_results["single_resume"] = False
            logger.warning("No test files available for single resume test")
        
        # Test 4: Multiple resume processing
        success_count, total_count = await self.test_multiple_resumes()
        test_results["multiple_resumes"] = (success_count, total_count)
        
        # Test 5: Error handling
        error_success, error_total = await self.test_error_handling()
        test_results["error_handling"] = (error_success, error_total)
        
        # Print summary
        self.print_test_summary(test_results)
        
        return test_results
    
    def print_test_summary(self, results):
        """Print a summary of all test results."""
        logger.info("\n" + "="*60)
        logger.info("VECTOR-ONLY RESUME PROCESSING TEST SUMMARY")
        logger.info("="*60)
        
        # Individual test results
        logger.info(f"Service Initialization: {'✅ PASS' if results['initialization'] else '❌ FAIL'}")
        logger.info(f"Point ID Consistency: {'✅ PASS' if results['point_id_consistency'] else '❌ FAIL'}")
        logger.info(f"Single Resume Processing: {'✅ PASS' if results['single_resume'] else '❌ FAIL'}")
        
        # Multiple resumes
        success_count, total_count = results['multiple_resumes']
        logger.info(f"Multiple Resume Processing: {success_count}/{total_count} successful")
        
        # Error handling
        error_success, error_total = results['error_handling']
        logger.info(f"Error Handling: {error_success}/{error_total} cases handled correctly")
        
        # Overall assessment
        basic_tests_passed = sum([
            results['initialization'],
            results['point_id_consistency'],
            results['single_resume']
        ])
        
        logger.info(f"\nBasic Tests: {basic_tests_passed}/3 passed")
        logger.info(f"Multiple Resume Success Rate: {success_count/total_count*100:.1f}%")
        logger.info(f"Error Handling Success Rate: {error_success/error_total*100:.1f}%")
        
        if basic_tests_passed == 3 and success_count > 0:
            logger.info("\n🎉 Vector-only resume processing is working correctly!")
        else:
            logger.info("\n⚠️ Some tests failed. Please check the logs above for details.")


async def main():
    """Main function to run the tests."""
    # Set up environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check required environment variables
    required_vars = ['QDRANT_URL', 'QDRANT_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        logger.error("Please ensure your .env file is properly configured.")
        return
    
    # Run tests
    test_runner = VectorOnlyResumeTestRunner()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
