#!/usr/bin/env python3
"""
Direct testing of resume processing endpoints without requiring a running server.

This script tests the resume processing functionality directly by importing
and calling the service functions, bypassing the HTTP layer.
"""

import asyncio
import os
import sys
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger
from services.api.clients.resume_service import (
    process_resume,
    process_resume_vector_only,
    ResumeProcessingService,
    ResumeVectorOnlyService
)

logger = get_logger(__name__)


class DirectResumeTestRunner:
    """Test runner for direct resume processing functionality."""
    
    def __init__(self):
        self.test_email = "<EMAIL>"
        self.test_s3_url = "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf"
        self.test_filename = "AbhishekBisht[5y_0m].pdf"
    
    def print_header(self):
        """Print test header."""
        print("=" * 80)
        print("🧪 DIRECT RESUME PROCESSING TESTS")
        print("=" * 80)
        print(f"Test Email: {self.test_email}")
        print(f"Test S3 URL: {self.test_s3_url}")
        print(f"Test Filename: {self.test_filename}")
        print()
    
    async def test_original_resume_processing(self):
        """Test the original complex resume processing."""
        print("📄 Testing Original Resume Processing (Complex)...")
        
        try:
            start_time = time.time()
            
            result = await process_resume(
                email=self.test_email,
                resume_s3_url=self.test_s3_url,
                filename=self.test_filename
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            if result.success:
                print("✅ Original resume processing succeeded!")
                print(f"   Processing Time: {result.processing_time:.2f} seconds")
                print(f"   Total Time: {total_time:.2f} seconds")
                print(f"   Profile Data: {result.profile_data.full_name if result.profile_data else 'N/A'}")
                print(f"   Skills Count: {len(result.profile_data.skills) if result.profile_data else 0}")
                return True
            else:
                print("❌ Original resume processing failed!")
                print(f"   Error Type: {result.error_type}")
                print(f"   Error Message: {result.error_message}")
                print(f"   Processing Time: {result.processing_time:.2f} seconds")
                return False
                
        except Exception as e:
            print(f"❌ Exception in original resume processing: {e}")
            return False
    
    async def test_vector_only_resume_processing(self):
        """Test the new vector-only resume processing."""
        print("\n🎯 Testing Vector-Only Resume Processing (Simplified)...")
        
        try:
            start_time = time.time()
            
            result = await process_resume_vector_only(
                email=self.test_email,
                resume_s3_url=self.test_s3_url,
                filename=self.test_filename
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            if result.success:
                print("✅ Vector-only resume processing succeeded!")
                print(f"   Point ID: {result.point_id}")
                print(f"   Text Length: {result.text_length} characters")
                print(f"   Processing Time: {result.processing_time:.2f} seconds")
                print(f"   Total Time: {total_time:.2f} seconds")
                return True
            else:
                print("❌ Vector-only resume processing failed!")
                print(f"   Error Type: {result.error_type}")
                print(f"   Error Message: {result.error_message}")
                print(f"   Processing Time: {result.processing_time:.2f} seconds")
                return False
                
        except Exception as e:
            print(f"❌ Exception in vector-only resume processing: {e}")
            return False
    
    async def test_service_initialization(self):
        """Test service initialization."""
        print("\n🔧 Testing Service Initialization...")
        
        # Test original service
        print("   Testing ResumeProcessingService...")
        try:
            service = ResumeProcessingService()
            await service.initialize()
            print("   ✅ ResumeProcessingService initialized successfully")
            original_service_ok = True
        except Exception as e:
            print(f"   ❌ ResumeProcessingService initialization failed: {e}")
            original_service_ok = False
        
        # Test vector-only service
        print("   Testing ResumeVectorOnlyService...")
        try:
            service = ResumeVectorOnlyService()
            await service.initialize()
            print("   ✅ ResumeVectorOnlyService initialized successfully")
            vector_service_ok = True
        except Exception as e:
            print(f"   ❌ ResumeVectorOnlyService initialization failed: {e}")
            vector_service_ok = False
        
        return original_service_ok, vector_service_ok
    
    async def test_environment_configuration(self):
        """Test environment configuration."""
        print("\n⚙️  Testing Environment Configuration...")
        
        required_vars = [
            'QDRANT_URL',
            'QDRANT_API_KEY',
            'DATABASE_URL'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"   ❌ Missing environment variables: {', '.join(missing_vars)}")
            return False
        else:
            print("   ✅ All required environment variables are set")
            return True
    
    async def run_all_tests(self):
        """Run all direct tests."""
        self.print_header()
        
        # Test environment
        env_ok = await self.test_environment_configuration()
        
        if not env_ok:
            print("\n❌ Environment configuration issues detected.")
            print("   Please check your .env file and ensure all required variables are set.")
            return
        
        # Test service initialization
        original_init_ok, vector_init_ok = await self.test_service_initialization()
        
        if not (original_init_ok or vector_init_ok):
            print("\n❌ Both services failed to initialize.")
            print("   Please check your Qdrant and database connections.")
            return
        
        # Test processing functions
        results = {}
        
        if original_init_ok:
            results['original'] = await self.test_original_resume_processing()
        else:
            print("\n⏭️  Skipping original resume processing (service initialization failed)")
            results['original'] = False
        
        if vector_init_ok:
            results['vector_only'] = await self.test_vector_only_resume_processing()
        else:
            print("\n⏭️  Skipping vector-only resume processing (service initialization failed)")
            results['vector_only'] = False
        
        # Print summary
        self.print_summary(results, original_init_ok, vector_init_ok)
    
    def print_summary(self, results, original_init_ok, vector_init_ok):
        """Print test summary."""
        print("\n" + "=" * 80)
        print("📊 TEST SUMMARY")
        print("=" * 80)
        
        print(f"Service Initialization:")
        print(f"  Original Service: {'✅ OK' if original_init_ok else '❌ FAILED'}")
        print(f"  Vector-Only Service: {'✅ OK' if vector_init_ok else '❌ FAILED'}")
        
        print(f"\nProcessing Tests:")
        print(f"  Original Processing: {'✅ PASSED' if results.get('original') else '❌ FAILED'}")
        print(f"  Vector-Only Processing: {'✅ PASSED' if results.get('vector_only') else '❌ FAILED'}")
        
        # Recommendations
        print(f"\n🎯 RECOMMENDATIONS:")
        
        if results.get('vector_only'):
            print("✅ Use the vector-only endpoint (/api/ingest/resume-simple) for robust processing")
        
        if results.get('original'):
            print("✅ Original endpoint (/api/ingest/resume) is also working")
        
        if not any(results.values()):
            print("❌ Both processing methods failed - check your environment configuration")
            print("   1. Verify Qdrant connection")
            print("   2. Verify database connection")
            print("   3. Check S3 access permissions")
        
        # Authentication note
        print(f"\n🔐 AUTHENTICATION NOTE:")
        print("   Both endpoints require JWT authentication with 'service_extract' role")
        print("   Use the diagnostic script to generate test tokens:")
        print("   python scripts/diagnose_api_issues.py --simple-token")


async def main():
    """Main test function."""
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Run tests
    test_runner = DirectResumeTestRunner()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
