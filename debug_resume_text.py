#!/usr/bin/env python3
"""
Debug script to see the full extracted resume text.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def debug_resume_text():
    """Extract and display the full resume text for debugging."""
    
    from services.api.clients.pdf_processor import PDFProcessor
    
    test_s3_url = "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf"
    
    print(f"🔍 Extracting full resume text for debugging")
    print(f"📎 S3 URL: {test_s3_url}")
    
    try:
        async with PDFProcessor() as processor:
            extracted_text = await processor.process_resume_pdf(test_s3_url)
        
        print(f"✅ Extracted {len(extracted_text)} characters")
        print("=" * 80)
        print("FULL EXTRACTED TEXT:")
        print("=" * 80)
        print(extracted_text)
        print("=" * 80)
        
        # Save to file for analysis
        with open("extracted_resume_text.txt", "w", encoding="utf-8") as f:
            f.write(extracted_text)
        
        print("💾 Saved full text to 'extracted_resume_text.txt'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(debug_resume_text())
