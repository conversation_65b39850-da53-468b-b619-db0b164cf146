#!/usr/bin/env python3
"""
Quick test script for vector-only resume processing.

This script provides a simple way to test the vector-only resume processing
functionality with a single test file from the s3://skill-assessment-test/pdfs/ bucket.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger
from services.api.clients.resume_service import process_resume_vector_only

logger = get_logger(__name__)


async def quick_test():
    """Run a quick test of the vector-only resume processing."""
    logger.info("Starting quick test of vector-only resume processing...")
    
    # Test configuration
    test_email = "<EMAIL>"
    test_s3_url = "https://skill-assessment-test.s3.amazonaws.com/pdfs/test_resume.pdf"
    test_filename = "test_resume.pdf"
    
    try:
        logger.info(f"Processing resume for {test_email}")
        logger.info(f"S3 URL: {test_s3_url}")
        
        # Process the resume
        result = await process_resume_vector_only(
            email=test_email,
            resume_s3_url=test_s3_url,
            filename=test_filename
        )
        
        # Check results
        if result.success:
            logger.info("🎉 SUCCESS! Vector-only resume processing completed successfully")
            logger.info(f"   Email: {result.email}")
            logger.info(f"   Point ID: {result.point_id}")
            logger.info(f"   Text Length: {result.text_length} characters")
            logger.info(f"   Processing Time: {result.processing_time:.2f} seconds")
            
            # Verify the vector was stored in Qdrant
            logger.info("\nVector storage verification:")
            logger.info("✅ Resume text extracted and converted to vector embedding")
            logger.info("✅ Vector stored in Qdrant 'resumes' collection")
            logger.info("✅ Minimal metadata stored for searchability")
            
            return True
            
        else:
            logger.error("❌ FAILED! Vector-only resume processing failed")
            logger.error(f"   Error Type: {result.error_type}")
            logger.error(f"   Error Message: {result.error_message}")
            logger.error(f"   Processing Time: {result.processing_time:.2f} seconds")
            
            return False
            
    except Exception as e:
        logger.error(f"❌ EXCEPTION! Unexpected error during processing: {e}")
        return False


async def test_with_custom_file():
    """Test with a custom S3 URL provided by the user."""
    print("\n" + "="*60)
    print("CUSTOM FILE TEST")
    print("="*60)
    
    # Get custom inputs
    custom_email = input("Enter test email (or press Enter for default): ").strip()
    if not custom_email:
        custom_email = "<EMAIL>"
    
    custom_s3_url = input("Enter S3 URL (or press Enter to skip): ").strip()
    if not custom_s3_url:
        print("Skipping custom file test.")
        return True
    
    custom_filename = input("Enter filename (optional): ").strip() or None
    
    try:
        logger.info(f"Processing custom resume for {custom_email}")
        logger.info(f"S3 URL: {custom_s3_url}")
        
        result = await process_resume_vector_only(
            email=custom_email,
            resume_s3_url=custom_s3_url,
            filename=custom_filename
        )
        
        if result.success:
            logger.info("🎉 Custom file processing successful!")
            logger.info(f"   Text Length: {result.text_length} characters")
            logger.info(f"   Processing Time: {result.processing_time:.2f} seconds")
            return True
        else:
            logger.error(f"❌ Custom file processing failed: {result.error_message}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Custom file processing exception: {e}")
        return False


def check_environment():
    """Check if the required environment variables are set."""
    required_vars = ['QDRANT_URL', 'QDRANT_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error("❌ Missing required environment variables:")
        for var in missing_vars:
            logger.error(f"   - {var}")
        logger.error("\nPlease ensure your .env file is properly configured.")
        return False
    
    logger.info("✅ Environment variables configured correctly")
    return True


async def main():
    """Main function."""
    print("="*60)
    print("VECTOR-ONLY RESUME PROCESSING QUICK TEST")
    print("="*60)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check environment
    if not check_environment():
        return
    
    # Run quick test
    print("\n" + "="*60)
    print("STANDARD TEST")
    print("="*60)
    
    success = await quick_test()
    
    if success:
        print("\n✅ Standard test passed! The vector-only resume processing is working correctly.")
        
        # Offer custom test
        run_custom = input("\nWould you like to test with a custom S3 URL? (y/N): ").strip().lower()
        if run_custom in ['y', 'yes']:
            await test_with_custom_file()
    else:
        print("\n❌ Standard test failed. Please check the logs above for details.")
    
    print("\n" + "="*60)
    print("TEST COMPLETED")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
