#!/usr/bin/env python3
"""
Debug script to see what sections are being extracted.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def debug_sections():
    """Debug section extraction."""
    
    from services.api.clients.pdf_processor import PDFProcessor
    from services.api.clients.resume_parser import ResumeParser
    
    test_s3_url = "https://skill-assessment-test.s3.amazonaws.com/pdfs/AbhishekBisht[5y_0m].pdf"
    
    print(f"🔍 Debugging section extraction")
    
    try:
        # Extract text
        async with PDFProcessor() as processor:
            extracted_text = await processor.process_resume_pdf(test_s3_url)
        
        # Parse with debug info
        parser = ResumeParser()
        
        # Find sections
        sections = parser.find_section_boundaries(extracted_text)
        
        print(f"📊 Found {len(sections)} sections:")
        for section_name, (start, end) in sections.items():
            print(f"  {section_name}: positions {start}-{end}")
        
        print("\n" + "=" * 80)
        
        # Show content of each section
        for section_name, (start, end) in sections.items():
            content = parser.extract_section_content(extracted_text, section_name, sections)
            print(f"\n📋 {section_name.upper()} SECTION:")
            print("-" * 40)
            print(content[:500] + ("..." if len(content) > 500 else ""))
            print("-" * 40)
        
        # Test skills parsing specifically
        if 'skills' in sections:
            skills_content = parser.extract_section_content(extracted_text, 'skills', sections)
            print(f"\n🎯 SKILLS PARSING TEST:")
            print(f"Raw skills content: {repr(skills_content[:200])}")
            skills = parser.parse_skills_section(skills_content)
            print(f"Parsed skills: {skills}")
        
        # Test experience parsing specifically
        if 'experience' in sections:
            exp_content = parser.extract_section_content(extracted_text, 'experience', sections)
            print(f"\n💼 EXPERIENCE PARSING TEST:")
            print(f"Raw experience content: {repr(exp_content[:200])}")
            experiences = parser.parse_experience_section(exp_content)
            print(f"Parsed experiences: {len(experiences)} found")
            for i, exp in enumerate(experiences):
                print(f"  {i+1}. {exp.job_title} at {exp.company_name} ({exp.dates})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(debug_sections())
